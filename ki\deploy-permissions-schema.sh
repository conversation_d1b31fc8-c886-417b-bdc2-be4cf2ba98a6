#!/bin/bash

# Deploy Enhanced SpiceDB Schema with CRUD Permissions
# This script deploys the updated schema.zed file to SpiceDB

set -e

# Configuration
SPICEDB_ENDPOINT="${SPICEDB_ENDPOINT:-localhost:50051}"
SPICEDB_TOKEN="${SPICEDB_TOKEN:-dev-token}"
SCHEMA_FILE="schema.zed"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Deploying Enhanced SpiceDB Schema with CRUD Permissions${NC}"
echo "=================================================="

# Check if schema file exists
if [ ! -f "$SCHEMA_FILE" ]; then
    echo -e "${RED}❌ Error: Schema file '$SCHEMA_FILE' not found${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Schema file: $SCHEMA_FILE${NC}"
echo -e "${YELLOW}🔗 SpiceDB endpoint: $SPICEDB_ENDPOINT${NC}"

# Check if zed CLI is available
if ! command -v zed &> /dev/null; then
    echo -e "${RED}❌ Error: 'zed' CLI tool not found${NC}"
    echo "Please install the SpiceDB CLI tools:"
    echo "  go install github.com/authzed/spicedb/cmd/zed@latest"
    exit 1
fi

# Validate schema syntax
echo -e "${YELLOW}🔍 Validating schema syntax...${NC}"
if zed validate "$SCHEMA_FILE"; then
    echo -e "${GREEN}✅ Schema syntax is valid${NC}"
else
    echo -e "${RED}❌ Schema validation failed${NC}"
    exit 1
fi

# Deploy schema to SpiceDB
echo -e "${YELLOW}📤 Deploying schema to SpiceDB...${NC}"
if zed schema write \
    --endpoint="$SPICEDB_ENDPOINT" \
    --token="$SPICEDB_TOKEN" \
    --insecure \
    "$SCHEMA_FILE"; then
    echo -e "${GREEN}✅ Schema deployed successfully${NC}"
else
    echo -e "${RED}❌ Schema deployment failed${NC}"
    exit 1
fi

# Verify deployment by reading back the schema
echo -e "${YELLOW}🔍 Verifying deployment...${NC}"
if zed schema read \
    --endpoint="$SPICEDB_ENDPOINT" \
    --token="$SPICEDB_TOKEN" \
    --insecure > /tmp/deployed_schema.zed; then
    echo -e "${GREEN}✅ Schema verification successful${NC}"
    echo -e "${YELLOW}📄 Deployed schema saved to /tmp/deployed_schema.zed${NC}"
else
    echo -e "${RED}❌ Schema verification failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Enhanced SpiceDB Schema Deployment Complete!${NC}"
echo ""
echo "The following CRUD permissions are now available:"
echo ""
echo "📋 User Entity:"
echo "  - create: admin + manager"
echo "  - view: admin + manager + viewer"
echo "  - edit: admin + manager"
echo "  - delete: admin"
echo ""
echo "🤖 Agent Entity:"
echo "  - create: admin + manager"
echo "  - view: admin + manager + viewer + creator"
echo "  - edit: admin + manager + creator"
echo "  - delete: admin + creator"
echo ""
echo "👥 Role Entity:"
echo "  - create: admin + manager"
echo "  - view: admin + manager + viewer"
echo "  - edit: admin + manager"
echo "  - delete: admin"
echo ""
echo "📝 Task Entity:"
echo "  - create: admin + editor + owner"
echo "  - view: viewer + editor + admin + owner + assignee"
echo "  - edit: editor + admin + owner"
echo "  - delete: admin + owner"
echo ""
echo "🌐 Global Settings:"
echo "  - manage_users: admin + owner"
echo "  - manage_agents: admin + owner"
echo "  - manage_tasks: admin + owner"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Update your application handlers to use the new permission checks"
echo "2. Apply permission middleware to your routes"
echo "3. Test the permission system with different user roles"
echo "4. Set up initial admin users and roles"
echo ""
echo -e "${YELLOW}Example Usage:${NC}"
echo "# Check if user can create a task"
echo "zed permission check task:task-123 create user:user-456 \\"
echo "  --endpoint=\"$SPICEDB_ENDPOINT\" \\"
echo "  --token=\"$SPICEDB_TOKEN\" \\"
echo "  --insecure"
echo ""
echo "# Grant admin role to a user for user management"
echo "zed relationship create global_settings:system admin_user user:admin-user-id \\"
echo "  --endpoint=\"$SPICEDB_ENDPOINT\" \\"
echo "  --token=\"$SPICEDB_TOKEN\" \\"
echo "  --insecure"
