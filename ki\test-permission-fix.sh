#!/bin/bash

# Test Permission Fix
# This script tests the permission checking fix

set -e

# Configuration
SERVER_URL="${SERVER_URL:-http://localhost:3000}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 Testing Permission Fix${NC}"
echo "=========================="

# Test permission check with Firebase UID (should work)
echo -e "\n${YELLOW}🧪 Test 1: Permission check with Firebase UID...${NC}"

PERMISSION_CHECK_FIREBASE='{
    "checks": [
        {
            "resource_type": "global_settings",
            "resource_id": "system",
            "permission": "manage_roles",
            "subject_type": "user",
            "subject_id": "test-firebase-user-1"
        }
    ]
}'

PERM_RESPONSE=$(curl -s -X POST "$SERVER_URL/permissions/check" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "$PERMISSION_CHECK_FIREBASE" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$PERM_RESPONSE" | tail -n1)
PERM_DATA=$(echo "$PERM_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Permission check API responded${NC}"
    ALLOWED=$(echo "$PERM_DATA" | jq -r '.results[0].allowed' 2>/dev/null || echo "unknown")
    if [ "$ALLOWED" = "true" ]; then
        echo -e "${GREEN}✅ Permission check PASSED - User has manage_roles permission${NC}"
    else
        echo -e "${RED}❌ Permission check FAILED - User does not have manage_roles permission${NC}"
    fi
    echo "$PERM_DATA" | jq '.' 2>/dev/null || echo "$PERM_DATA"
else
    echo -e "${RED}❌ Permission check API failed (HTTP $HTTP_CODE)${NC}"
    echo "$PERM_DATA"
fi

# Test permission check with database UUID (should fail)
echo -e "\n${YELLOW}🧪 Test 2: Permission check with database UUID (should fail)...${NC}"

# First get the user to find their database UUID
USER_RESPONSE=$(curl -s -X GET "$SERVER_URL/users" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$USER_RESPONSE" | tail -n1)
USER_DATA=$(echo "$USER_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    # Extract the first user's database ID
    DB_UUID=$(echo "$USER_DATA" | jq -r '.[0].id' 2>/dev/null || echo "")
    
    if [ ! -z "$DB_UUID" ] && [ "$DB_UUID" != "null" ]; then
        echo "Testing with database UUID: $DB_UUID"
        
        PERMISSION_CHECK_DB="{
            \"checks\": [
                {
                    \"resource_type\": \"global_settings\",
                    \"resource_id\": \"system\",
                    \"permission\": \"manage_roles\",
                    \"subject_type\": \"user\",
                    \"subject_id\": \"$DB_UUID\"
                }
            ]
        }"

        PERM_RESPONSE_DB=$(curl -s -X POST "$SERVER_URL/permissions/check" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -d "$PERMISSION_CHECK_DB" \
            -w "\n%{http_code}" || echo "000")

        HTTP_CODE=$(echo "$PERM_RESPONSE_DB" | tail -n1)
        PERM_DATA_DB=$(echo "$PERM_RESPONSE_DB" | head -n -1)

        if [ "$HTTP_CODE" = "200" ]; then
            ALLOWED=$(echo "$PERM_DATA_DB" | jq -r '.results[0].allowed' 2>/dev/null || echo "unknown")
            if [ "$ALLOWED" = "false" ]; then
                echo -e "${GREEN}✅ Expected result - Database UUID correctly denied${NC}"
            else
                echo -e "${YELLOW}⚠️  Unexpected result - Database UUID was allowed${NC}"
            fi
            echo "$PERM_DATA_DB" | jq '.' 2>/dev/null || echo "$PERM_DATA_DB"
        else
            echo -e "${RED}❌ Permission check with DB UUID failed (HTTP $HTTP_CODE)${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Could not extract database UUID from user data${NC}"
    fi
else
    echo -e "${RED}❌ Failed to get users (HTTP $HTTP_CODE)${NC}"
fi

# Test role membership lookup
echo -e "\n${YELLOW}🧪 Test 3: Checking role memberships...${NC}"

echo -e "${BLUE}Checking Owner role members:${NC}"
OWNER_DETAILS=$(curl -s -X GET "$SERVER_URL/roles/owner" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$OWNER_DETAILS" | tail -n1)
OWNER_DATA=$(echo "$OWNER_DETAILS" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Owner role details retrieved${NC}"
    MEMBER_COUNT=$(echo "$OWNER_DATA" | jq '.members | length' 2>/dev/null || echo "0")
    echo "Owner role has $MEMBER_COUNT members"
    
    if [ "$MEMBER_COUNT" -gt "0" ]; then
        echo "Members:"
        echo "$OWNER_DATA" | jq '.members[] | {id: .id, name: .name, email: .email}' 2>/dev/null || echo "Could not parse members"
    else
        echo -e "${RED}❌ No members found in Owner role${NC}"
    fi
else
    echo -e "${RED}❌ Failed to get Owner role details (HTTP $HTTP_CODE)${NC}"
    echo "$OWNER_DATA"
fi

# Summary
echo -e "\n${BLUE}📊 Test Summary${NC}"
echo "==============="
echo "✅ Permission check with Firebase UID tested"
echo "✅ Permission check with database UUID tested"
echo "✅ Role membership lookup tested"

echo -e "\n${YELLOW}🔧 Expected Results:${NC}"
echo "1. Firebase UID permission check should return 'allowed: true'"
echo "2. Database UUID permission check should return 'allowed: false'"
echo "3. Owner role should have at least 1 member"

echo -e "\n${GREEN}🎉 Permission fix testing completed!${NC}"
