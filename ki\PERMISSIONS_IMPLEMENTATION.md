# Comprehensive SpiceDB-Based Permissions System Implementation

## Overview

This document outlines the comprehensive SpiceDB-based permissions system implemented for the @ki/ server, supporting full CRUD (Create, Read, Update, Delete) operations for Users, Agents, Roles, and Tasks.

## Architecture

### 1. SpiceDB Schema (`ki/schema.zed`)

The schema has been enhanced to support comprehensive CRUD permissions for all entities:

#### User Entity
- **Relations**: `admin_user`, `admin_role`, `manager_user`, `manager_role`, `viewer_user`, `viewer_role`
- **CRUD Permissions**:
  - `create`: admin + manager
  - `view`: admin + manager + viewer
  - `edit`: admin + manager
  - `delete`: admin

#### Agent Entity
- **Relations**: `admin_user`, `admin_role`, `manager_user`, `manager_role`, `viewer_user`, `viewer_role`, `creator`
- **CRUD Permissions**:
  - `create`: admin + manager
  - `view`: admin + manager + viewer + creator
  - `edit`: admin + manager + creator
  - `delete`: admin + creator

#### Role Entity
- **Relations**: `member`, `parent`, `admin_user`, `admin_role`, `manager_user`, `manager_role`, `viewer_user`, `viewer_role`
- **CRUD Permissions**:
  - `create`: admin + manager
  - `view`: admin + manager + viewer
  - `edit`: admin + manager
  - `delete`: admin

#### Task Entity
- **Relations**: `owner_user`, `owner_role`, `assignee`, `viewer_user`, `viewer_role`, `editor_user`, `editor_role`, `admin_user`, `admin_role`
- **CRUD Permissions**:
  - `create`: admin + editor + owner
  - `view`: viewer + editor + admin + owner + assignee
  - `edit`: editor + admin + owner
  - `delete`: admin + owner

#### Global Settings
- **New Permissions**:
  - `manage_users`: admin + owner
  - `manage_agents`: admin + owner
  - `manage_tasks`: admin + owner

### 2. Enhanced SpiceDB Service (`ki/src/services/spicedb_service.rs`)

New methods added for comprehensive CRUD permission management:

#### Resource Permission Management
```rust
// Grant CRUD permission to a user for a specific resource
pub async fn grant_resource_permission(
    &self,
    resource_type: &str,
    resource_id: &str,
    permission: &str,
    user_id: &str,
) -> Result<()>

// Revoke CRUD permission from a user for a specific resource
pub async fn revoke_resource_permission(
    &self,
    resource_type: &str,
    resource_id: &str,
    permission: &str,
    user_id: &str,
) -> Result<()>

// Set creator relationship for a resource
pub async fn set_creator(
    &self,
    resource_type: &str,
    resource_id: &str,
    creator_id: &str,
) -> Result<()>
```

#### Optimized Batch Operations
```rust
// Batch check permissions with optimized SpiceDB calls
pub async fn batch_check_permissions_optimized(
    &self,
    checks: Vec<PermissionCheck>
) -> Result<Vec<PermissionCheckResult>>
```

### 3. Permission Checking Middleware (`ki/src/api/middleware/permission_check.rs`)

Comprehensive middleware system for CRUD permission validation:

#### Core Components
- **UserContext**: Extracted from session for permission checks
- **CrudPermission**: Enum for Create, Read, Update, Delete operations
- **Permission Checking Functions**: For both resource-specific and global permissions

#### Helper Functions
```rust
// Extract user context from session
pub async fn extract_user_context(
    state: &ServerState,
    jar: &CookieJar,
) -> Result<UserContext, StatusCode>

// Check resource-specific permission
pub async fn check_permission(
    spicedb: &SpiceDBService,
    user_context: &UserContext,
    resource_type: &str,
    resource_id: &str,
    permission: &str,
) -> Result<bool, StatusCode>

// Check global permission (for creation, etc.)
pub async fn check_global_permission(
    spicedb: &SpiceDBService,
    user_context: &UserContext,
    resource_type: &str,
    permission: &str,
) -> Result<bool, StatusCode>
```

#### Middleware Factories
Pre-built middleware functions for each CRUD operation:
- `users_create_permission()`, `users_read_permission()`, etc.
- `agents_create_permission()`, `agents_read_permission()`, etc.
- `roles_create_permission()`, `roles_read_permission()`, etc.
- `tasks_create_permission()`, `tasks_read_permission()`, etc.

### 4. Permission Helper in Handlers (`ki/src/api/handlers/user_handlers.rs`)

Example implementation of permission checking in handlers:

```rust
/// Helper function to check user permissions
async fn check_user_permission(
    server_state: &ServerState,
    jar: &CookieJar,
    resource_type: &str,
    resource_id: Option<&str>,
    permission: &str,
) -> Result<String, (StatusCode, String)>
```

## Usage Examples

### 1. Applying Permission Middleware to Routes

```rust
// In routes.rs - example of how to apply permission middleware
let protected_routes = Router::new()
    .route("/users", post(create_user))
    .route_layer(middleware::from_fn_with_state(
        server_state.clone(), 
        users_create_permission()
    ))
    .route("/users", get(get_users))
    .route_layer(middleware::from_fn_with_state(
        server_state.clone(), 
        users_read_permission()
    ));
```

### 2. Manual Permission Checking in Handlers

```rust
pub async fn create_user_with_permissions(
    State(server_state): State<ServerState>,
    Json(new_user): Json<NewUser>,
    jar: CookieJar,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    // Check permission to create users
    let user_id = check_user_permission(
        &server_state,
        &jar,
        "global_settings",
        Some("system"),
        "manage_users",
    ).await?;

    // Create user logic...
    let repo = UserRepository::new(server_state.db.clone());
    let user = repo.create(new_user).await?;

    // Grant creator permissions
    if let Some(spicedb) = &server_state.spicedb {
        spicedb.grant_resource_permission(
            "user",
            &user.id.to_string(),
            "view",
            &user_id,
        ).await?;
    }

    Ok((StatusCode::CREATED, Json(user.into())))
}
```

### 3. Batch Permission Checking

```rust
// Check multiple permissions at once
let checks = vec![
    PermissionCheck {
        resource_type: "user".to_string(),
        resource_id: "user-123".to_string(),
        permission: "view".to_string(),
        subject_type: "user".to_string(),
        subject_id: current_user_id.clone(),
    },
    PermissionCheck {
        resource_type: "task".to_string(),
        resource_id: "task-456".to_string(),
        permission: "edit".to_string(),
        subject_type: "user".to_string(),
        subject_id: current_user_id.clone(),
    },
];

let results = spicedb.batch_check_permissions_optimized(checks).await?;
```

## Key Features

### 1. **Single Source of Truth**
- SpiceDB is the authoritative source for all authorization relationships
- No dual system anti-pattern - SQL database only stores metadata

### 2. **Atomic Operations**
- All permission changes use atomic SpiceDB transactions
- Hierarchy changes maintain consistency

### 3. **Optimized Performance**
- BatchCheckPermission for multiple permission checks
- ZedToken aggregation for read-after-write consistency

### 4. **Comprehensive CRUD Support**
- Full Create, Read, Update, Delete permissions for all entities
- Resource-specific and global permission patterns

### 5. **Role-Based Access Control**
- Hierarchical role system with inheritance
- Admin, Manager, Viewer role patterns

### 6. **Creator Permissions**
- Automatic creator relationships for owned resources
- Special permissions for resource creators (especially agents)

## Implementation Status

✅ **Completed:**
- Enhanced SpiceDB schema with CRUD permissions
- SpiceDB service methods for resource permission management
- Permission checking middleware infrastructure
- Helper functions for permission validation
- Example implementations in user handlers

🔄 **Next Steps:**
1. Apply permission middleware to all routes
2. Update all CRUD handlers to include permission checks
3. Implement automatic permission granting on resource creation
4. Add comprehensive test coverage
5. Deploy updated schema to SpiceDB

## Security Considerations

1. **Default Deny**: All operations default to denied unless explicitly permitted
2. **Session Validation**: All permission checks validate session tokens
3. **Resource Isolation**: Users can only access resources they have explicit permissions for
4. **Audit Trail**: All permission changes are logged through SpiceDB
5. **Token Security**: ZedTokens are never exposed to external clients

This implementation provides a robust, scalable, and secure foundation for comprehensive CRUD permissions across all entities in the @ki/ server system.
