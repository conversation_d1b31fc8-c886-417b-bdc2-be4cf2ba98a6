#!/bin/bash

# Test Role Assignment Script
# This script helps test and debug the role assignment system

set -e

# Configuration
SERVER_URL="${SERVER_URL:-http://localhost:3000}"
SPICEDB_ENDPOINT="${SPICEDB_ENDPOINT:-localhost:50051}"
SPICEDB_TOKEN="${SPICEDB_TOKEN:-dev-token}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Role Assignment System${NC}"
echo "=================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo -e "${YELLOW}📋 Checking dependencies...${NC}"

if ! command_exists curl; then
    echo -e "${RED}❌ curl is required but not installed${NC}"
    exit 1
fi

if ! command_exists zed; then
    echo -e "${YELLOW}⚠️  zed CLI not found - SpiceDB direct queries will be skipped${NC}"
    ZED_AVAILABLE=false
else
    ZED_AVAILABLE=true
fi

echo -e "${GREEN}✅ Dependencies checked${NC}"

# Test 1: Check sync status
echo -e "\n${YELLOW}🔍 Test 1: Checking sync status...${NC}"
SYNC_RESPONSE=$(curl -s -X GET "$SERVER_URL/sync/status" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$SYNC_RESPONSE" | tail -n1)
SYNC_DATA=$(echo "$SYNC_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Sync status retrieved successfully${NC}"
    echo "$SYNC_DATA" | jq '.' 2>/dev/null || echo "$SYNC_DATA"
else
    echo -e "${RED}❌ Failed to get sync status (HTTP $HTTP_CODE)${NC}"
    echo "$SYNC_DATA"
fi

# Test 2: Check SpiceDB health
echo -e "\n${YELLOW}🏥 Test 2: Checking SpiceDB health...${NC}"
HEALTH_RESPONSE=$(curl -s -X GET "$SERVER_URL/sync/health" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$HEALTH_RESPONSE" | tail -n1)
HEALTH_DATA=$(echo "$HEALTH_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ SpiceDB health check successful${NC}"
    echo "$HEALTH_DATA" | jq '.' 2>/dev/null || echo "$HEALTH_DATA"
else
    echo -e "${RED}❌ SpiceDB health check failed (HTTP $HTTP_CODE)${NC}"
    echo "$HEALTH_DATA"
fi

# Test 3: Trigger manual sync
echo -e "\n${YELLOW}🔄 Test 3: Triggering manual synchronization...${NC}"
TRIGGER_RESPONSE=$(curl -s -X POST "$SERVER_URL/sync/trigger" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$TRIGGER_RESPONSE" | tail -n1)
TRIGGER_DATA=$(echo "$TRIGGER_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Manual sync triggered successfully${NC}"
    echo "$TRIGGER_DATA" | jq '.' 2>/dev/null || echo "$TRIGGER_DATA"
else
    echo -e "${RED}❌ Failed to trigger sync (HTTP $HTTP_CODE)${NC}"
    echo "$TRIGGER_DATA"
fi

# Test 4: Check role memberships via SpiceDB (if zed is available)
if [ "$ZED_AVAILABLE" = true ]; then
    echo -e "\n${YELLOW}👥 Test 4: Checking role memberships in SpiceDB...${NC}"

    echo -e "${BLUE}Checking Owner role members:${NC}"
    if zed relationship read role:owner member \
        --endpoint="$SPICEDB_ENDPOINT" \
        --token="$SPICEDB_TOKEN" \
        --insecure 2>/dev/null; then
        echo -e "${GREEN}✅ Owner role members retrieved${NC}"
    else
        echo -e "${YELLOW}⚠️  No Owner role members found or query failed${NC}"
    fi

    echo -e "\n${BLUE}Checking Member role members:${NC}"
    if zed relationship read role:member member \
        --endpoint="$SPICEDB_ENDPOINT" \
        --token="$SPICEDB_TOKEN" \
        --insecure 2>/dev/null; then
        echo -e "${GREEN}✅ Member role members retrieved${NC}"
    else
        echo -e "${YELLOW}⚠️  No Member role members found or query failed${NC}"
    fi

    echo -e "\n${BLUE}Checking global permissions:${NC}"
    if zed relationship read global_settings:system \
        --endpoint="$SPICEDB_ENDPOINT" \
        --token="$SPICEDB_TOKEN" \
        --insecure 2>/dev/null; then
        echo -e "${GREEN}✅ Global permissions retrieved${NC}"
    else
        echo -e "${YELLOW}⚠️  No global permissions found or query failed${NC}"
    fi
else
    echo -e "\n${YELLOW}⏭️  Test 4: Skipped (zed CLI not available)${NC}"
fi

# Test 4.5: Check role details via API
echo -e "\n${YELLOW}📋 Test 4.5: Checking role details via API...${NC}"

echo -e "${BLUE}Checking Owner role details:${NC}"
OWNER_DETAILS=$(curl -s -X GET "$SERVER_URL/roles/owner" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$OWNER_DETAILS" | tail -n1)
OWNER_DATA=$(echo "$OWNER_DETAILS" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Owner role details retrieved${NC}"
    echo "$OWNER_DATA" | jq '.members' 2>/dev/null || echo "Members data: $OWNER_DATA"
else
    echo -e "${RED}❌ Failed to get Owner role details (HTTP $HTTP_CODE)${NC}"
    echo "$OWNER_DATA"
fi

echo -e "\n${BLUE}Checking Member role details:${NC}"
MEMBER_DETAILS=$(curl -s -X GET "$SERVER_URL/roles/member" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$MEMBER_DETAILS" | tail -n1)
MEMBER_DATA=$(echo "$MEMBER_DETAILS" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Member role details retrieved${NC}"
    echo "$MEMBER_DATA" | jq '.members' 2>/dev/null || echo "Members data: $MEMBER_DATA"
else
    echo -e "${RED}❌ Failed to get Member role details (HTTP $HTTP_CODE)${NC}"
    echo "$MEMBER_DATA"
fi

# Test 5: Test permission checking
echo -e "\n${YELLOW}🔐 Test 5: Testing permission checks...${NC}"

# Example permission check payload
PERMISSION_CHECK='{
    "checks": [
        {
            "resource_type": "global_settings",
            "resource_id": "system",
            "permission": "manage_roles",
            "subject_type": "user",
            "subject_id": "test-user-id"
        }
    ]
}'

PERM_RESPONSE=$(curl -s -X POST "$SERVER_URL/permissions/check" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "$PERMISSION_CHECK" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$PERM_RESPONSE" | tail -n1)
PERM_DATA=$(echo "$PERM_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Permission check API working${NC}"
    echo "$PERM_DATA" | jq '.' 2>/dev/null || echo "$PERM_DATA"
else
    echo -e "${RED}❌ Permission check failed (HTTP $HTTP_CODE)${NC}"
    echo "$PERM_DATA"
fi

# Summary
echo -e "\n${BLUE}📊 Test Summary${NC}"
echo "==============="
echo -e "Server URL: $SERVER_URL"
echo -e "SpiceDB Endpoint: $SPICEDB_ENDPOINT"
echo -e "ZED CLI Available: $ZED_AVAILABLE"

echo -e "\n${YELLOW}🔧 Troubleshooting Tips:${NC}"
echo "1. If sync status fails, check if Ki server is running"
echo "2. If SpiceDB health fails, check if SpiceDB is running and accessible"
echo "3. If manual sync fails, check server logs for detailed errors"
echo "4. If role memberships are empty, users may need to re-authenticate"
echo "5. If permission checks fail, verify the schema is deployed correctly"

echo -e "\n${YELLOW}🚀 Next Steps:${NC}"
echo "1. Check the Ki server logs for detailed sync information"
echo "2. Test with actual user authentication in the frontend"
echo "3. Verify role assignments in the admin panel"
echo "4. Test CRUD operations with different user roles"

echo -e "\n${GREEN}🎉 Role assignment testing completed!${NC}"
