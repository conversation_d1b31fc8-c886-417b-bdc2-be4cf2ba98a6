use anyhow::Result;
use chrono::Utc;
use rs_firebase_admin_sdk::auth::token::jwt::JWToken;
use tracing::{info, warn};

use crate::db::{
    connection::Database,
    models::{NewUser, UpdateUser, User},
    repositories::UserRepository,
};
use crate::services::spicedb_service::SpiceDBService;

/// User service for handling automatic user creation and management
pub struct UserService {
    user_repo: UserRepository,
}

impl UserService {
    /// Create a new user service
    pub fn new(db: Database) -> Self {
        Self {
            user_repo: UserRepository::new(db),
        }
    }

    /// Ensure user exists for Firebase authentication
    /// Creates a new user if one doesn't exist, or updates last_login if it does
    /// Also handles first user bootstrapping with owner permissions
    pub async fn ensure_user_exists(&self, firebase_token: &JWToken, spicedb: Option<&SpiceDBService>) -> Result<User> {
        let firebase_user_id = &firebase_token.critical_claims.sub;

        // Try to find existing user by Firebase user ID
        match self.user_repo.get_by_user_id(firebase_user_id).await? {
            Some(existing_user) => {
                // User exists, update last_login
                info!("User {} already exists, updating last login", firebase_user_id);

                let update_user = UpdateUser {
                    email: None,
                    display_name: None,
                    photo_url: None,
                    last_login: Some(Utc::now()),
                };

                self.user_repo.update(existing_user.id, update_user).await
            }
            None => {
                // User doesn't exist, create new user from Firebase token
                info!("Creating new user for Firebase user {}", firebase_user_id);

                // Check if this is the first user (for bootstrapping)
                let is_first_user = self.is_first_user().await?;

                let new_user = self.create_user_from_firebase_token(firebase_token)?;
                let created_user = self.user_repo.create(new_user).await?;

                // If this is the first user and SpiceDB is available, grant owner permissions
                if is_first_user {
                    if let Some(spicedb_service) = spicedb {
                        if let Err(e) = self.bootstrap_first_user_permissions(&created_user.user_id, spicedb_service).await {
                            warn!("Failed to bootstrap first user permissions: {}", e);
                            // Don't fail user creation if permission bootstrapping fails
                        }
                    } else {
                        warn!("First user created but SpiceDB not available for permission bootstrapping");
                    }
                } else {
                    // For non-first users, sync with Member role assignment
                    if let Some(spicedb_service) = spicedb {
                        let sync_service = crate::services::sync_service::SyncService::new(
                            self.user_repo.get_database().clone(),
                            spicedb_service.clone()
                        );
                        if let Err(e) = sync_service.sync_user(created_user.id).await {
                            warn!("Failed to sync new user with Member role: {}", e);
                        }
                    }
                }

                Ok(created_user)
            }
        }
    }

    /// Create a NewUser from Firebase token claims
    fn create_user_from_firebase_token(&self, firebase_token: &JWToken) -> Result<NewUser> {
        let firebase_user_id = firebase_token.critical_claims.sub.clone();
        
        // Extract email from Firebase token
        let email = firebase_token.all_claims
            .get("email")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .unwrap_or_else(|| {
                warn!("No email found in Firebase token for user {}", firebase_user_id);
                // Use a placeholder email if none is provided
                format!("{}@firebase.user", firebase_user_id)
            });

        // Extract display name from Firebase token
        let display_name = firebase_token.all_claims
            .get("name")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        // Extract photo URL from Firebase token
        let photo_url = firebase_token.all_claims
            .get("picture")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        Ok(NewUser {
            user_id: firebase_user_id,
            email,
            display_name,
            photo_url,
        })
    }

    /// Create a user manually (without Firebase ID initially)
    pub async fn create_manual_user(&self, email: String, display_name: Option<String>, photo_url: Option<String>) -> Result<User> {
        // Generate a temporary user_id for manual users
        // This can be updated later when they authenticate with Firebase
        let temp_user_id = format!("manual_{}", uuid::Uuid::new_v4());
        
        let new_user = NewUser {
            user_id: temp_user_id,
            email,
            display_name,
            photo_url,
        };

        self.user_repo.create(new_user).await
    }

    /// Update a manual user's Firebase ID when they authenticate
    pub async fn link_firebase_user(&self, _user_id: uuid::Uuid, firebase_user_id: String) -> Result<User> {
        // Check if the Firebase user ID is already in use
        if let Some(_existing_user) = self.user_repo.get_by_user_id(&firebase_user_id).await? {
            return Err(anyhow::anyhow!("Firebase user ID {} is already linked to another user", firebase_user_id));
        }

        // Note: We need to add a method to update the user_id field
        // For now, we'll return an error indicating this functionality needs to be implemented
        Err(anyhow::anyhow!("Firebase user linking not yet implemented - requires database schema update"))
    }

    /// Get user repository for direct access when needed
    pub fn get_user_repo(&self) -> &UserRepository {
        &self.user_repo
    }

    /// Check if this is the first user in the system
    async fn is_first_user(&self) -> Result<bool> {
        let user_count = self.user_repo.count().await?;
        Ok(user_count <= 1) // <= 1 because we might have just created the user
    }

    /// Bootstrap permissions for the first user (grant owner permissions)
    async fn bootstrap_first_user_permissions(&self, firebase_user_id: &str, spicedb: &SpiceDBService) -> Result<()> {
        info!("Bootstrapping owner permissions for first user: {}", firebase_user_id);

        // Use the sync service to assign Owner role to the first user
        let sync_service = crate::services::sync_service::SyncService::new(
            self.user_repo.get_database().clone(),
            spicedb.clone()
        );

        if let Err(e) = sync_service.assign_owner_role(firebase_user_id).await {
            return Err(anyhow::anyhow!("Failed to assign owner role to first user: {}", e));
        }

        info!("Successfully assigned owner role to first user: {}", firebase_user_id);
        Ok(())
    }
}
