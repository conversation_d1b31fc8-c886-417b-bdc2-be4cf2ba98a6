use anyhow::Result;
use chrono::Utc;
use uuid::Uuid;

use crate::db::{
    connection::{Database, DatabaseConnection},
    models::{NewUser, UpdateUser, User},
};

/// User repository
pub struct UserRepository {
    db: Database,
}

impl UserRepository {
    /// Create a new user repository
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new user
    pub async fn create(&self, new_user: NewUser) -> Result<User> {
        let id = Uuid::new_v4();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let user = sqlx::query_as::<_, User>(
                    r#"
                    INSERT INTO users (id, user_id, email, display_name, photo_url)
                    VALUES (?, ?, ?, ?, ?)
                    RETURNING id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    "#
                )
                .bind(id)
                .bind(&new_user.user_id)
                .bind(&new_user.email)
                .bind(&new_user.display_name)
                .bind(&new_user.photo_url)
                .fetch_one(pool)
                .await?;

                Ok(user)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let user = sqlx::query_as::<_, User>(
                    r#"
                    INSERT INTO users (id, user_id, email, display_name, photo_url)
                    VALUES ($1, $2, $3, $4, $5)
                    RETURNING id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    "#
                )
                .bind(id)
                .bind(&new_user.user_id)
                .bind(&new_user.email)
                .bind(&new_user.display_name)
                .bind(&new_user.photo_url)
                .fetch_one(pool)
                .await?;

                Ok(user)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get all users
    pub async fn get_all(&self) -> Result<Vec<User>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let users = sqlx::query_as::<_, User>(
                    r#"
                    SELECT id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    FROM users
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(users)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let users = sqlx::query_as::<_, User>(
                    r#"
                    SELECT id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    FROM users
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(users)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get a user by ID
    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<User>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let user = sqlx::query_as::<_, User>(
                    r#"
                    SELECT id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    FROM users
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(user)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let user = sqlx::query_as::<_, User>(
                    r#"
                    SELECT id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    FROM users
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(user)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get a user by Firebase user ID
    pub async fn get_by_user_id(&self, user_id: &str) -> Result<Option<User>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let user = sqlx::query_as::<_, User>(
                    r#"
                    SELECT id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    FROM users
                    WHERE user_id = ?
                    "#
                )
                .bind(user_id)
                .fetch_optional(pool)
                .await?;

                Ok(user)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let user = sqlx::query_as::<_, User>(
                    r#"
                    SELECT id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    FROM users
                    WHERE user_id = $1
                    "#
                )
                .bind(user_id)
                .fetch_optional(pool)
                .await?;

                Ok(user)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update a user
    pub async fn update(&self, id: Uuid, update_user: UpdateUser) -> Result<User> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // For SQLite, we'll use a simpler approach similar to PostgreSQL
                // Get current user first to check if it exists
                let current = self.get_by_id(id).await?.ok_or_else(|| anyhow::anyhow!("User not found"))?;

                let email = update_user.email.unwrap_or(current.email);
                let display_name = update_user.display_name.or(current.display_name);
                let photo_url = update_user.photo_url.or(current.photo_url);
                let last_login = update_user.last_login.or(current.last_login);

                let user = sqlx::query_as::<_, User>(
                    r#"
                    UPDATE users SET
                        email = ?,
                        display_name = ?,
                        photo_url = ?,
                        last_login = ?
                    WHERE id = ?
                    RETURNING id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    "#
                )
                .bind(&email)
                .bind(&display_name)
                .bind(&photo_url)
                .bind(&last_login)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(user)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // For PostgreSQL, we'll use a simpler approach with COALESCE
                let user = sqlx::query_as::<_, User>(
                    r#"
                    UPDATE users SET
                        email = COALESCE($2, email),
                        display_name = COALESCE($3, display_name),
                        photo_url = COALESCE($4, photo_url),
                        last_login = COALESCE($5, last_login)
                    WHERE id = $1
                    RETURNING id, user_id, email, display_name, photo_url, created_at, updated_at, last_login
                    "#
                )
                .bind(id)
                .bind(&update_user.email)
                .bind(&update_user.display_name)
                .bind(&update_user.photo_url)
                .bind(&update_user.last_login)
                .fetch_one(pool)
                .await?;

                Ok(user)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete a user
    pub async fn delete(&self, id: Uuid) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM users WHERE id = ?")
                    .bind(id)
                    .execute(pool)
                    .await?;

                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query!("DELETE FROM users WHERE id = $1", id)
                    .execute(pool)
                    .await?;

                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Create or update user (upsert based on Firebase user_id)
    pub async fn upsert(&self, new_user: NewUser) -> Result<User> {
        // Try to find existing user by Firebase user_id
        if let Some(existing_user) = self.get_by_user_id(&new_user.user_id).await? {
            // Update existing user
            let update_user = UpdateUser {
                email: Some(new_user.email),
                display_name: new_user.display_name,
                photo_url: new_user.photo_url,
                last_login: Some(Utc::now()),
            };
            self.update(existing_user.id, update_user).await
        } else {
            // Create new user
            self.create(new_user).await
        }
    }

    /// Count total number of users
    pub async fn count(&self) -> Result<i64> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
                    .fetch_one(pool)
                    .await?;
                Ok(count)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let count = sqlx::query!("SELECT COUNT(*) as count FROM users")
                    .fetch_one(pool)
                    .await?
                    .count
                    .unwrap_or(0);
                Ok(count)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}
