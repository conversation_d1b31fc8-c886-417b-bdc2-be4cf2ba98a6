#!/bin/bash

# Deploy SpiceDB schema
# This script deploys the schema.zed file to SpiceDB

set -e

SPICEDB_ENDPOINT=${SPICEDB_ENDPOINT:-"localhost:50051"}
SPICEDB_TOKEN=${SPICEDB_TOKEN:-"somerandomkeyhere"}
SCHEMA_FILE="schema.zed"

echo "Deploying SpiceDB schema..."
echo "Endpoint: $SPICEDB_ENDPOINT"
echo "Schema file: $SCHEMA_FILE"

# Check if zed CLI is available
if ! command -v zed &> /dev/null; then
    echo "Error: zed CLI not found. Please install SpiceDB CLI tools."
    echo "Visit: https://github.com/authzed/spicedb/releases"
    exit 1
fi

# Check if schema file exists
if [ ! -f "$SCHEMA_FILE" ]; then
    echo "Error: Schema file $SCHEMA_FILE not found."
    exit 1
fi

# Deploy schema
echo "Deploying schema to SpiceDB..."
zed schema write \
    --endpoint "$SPICEDB_ENDPOINT" \
    --token "$SPICEDB_TOKEN" \
    --insecure \
    "$SCHEMA_FILE"

echo "Schema deployment completed successfully!"

# Optionally validate the schema
echo "Validating deployed schema..."
zed schema read \
    --endpoint "$SPICEDB_ENDPOINT" \
    --token "$SPICEDB_TOKEN" \
    --insecure

echo "Schema validation completed!"
