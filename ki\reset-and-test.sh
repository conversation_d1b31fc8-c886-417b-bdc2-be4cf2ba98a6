#!/bin/bash

# Reset Database and Test Role Assignment
# This script resets the database and tests the role assignment fixes

set -e

# Configuration
SERVER_URL="${SERVER_URL:-http://localhost:3000}"
SPICEDB_ENDPOINT="${SPICEDB_ENDPOINT:-localhost:50051}"
SPICEDB_TOKEN="${SPICEDB_TOKEN:-dev-token}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Reset Database and Test Role Assignment${NC}"
echo "=============================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if server is running
echo -e "${YELLOW}🔍 Checking if Ki server is running...${NC}"
if curl -s "$SERVER_URL/health" >/dev/null 2>&1; then
    echo -e "${RED}❌ Ki server is running. Please stop it first.${NC}"
    echo "Run: pkill -f 'cargo run' or stop the server manually"
    exit 1
else
    echo -e "${GREEN}✅ Ki server is not running${NC}"
fi

# Step 1: Reset SQLite database
echo -e "\n${YELLOW}🗄️  Step 1: Resetting SQLite database...${NC}"

if [ -f "ki.db" ]; then
    echo "Removing existing ki.db..."
    rm ki.db
fi

# Create empty database file
touch ki.db
echo -e "${GREEN}✅ SQLite database reset${NC}"

# Step 2: Reset SpiceDB (if zed is available)
if command_exists zed; then
    echo -e "\n${YELLOW}🧹 Step 2: Clearing SpiceDB relationships...${NC}"
    
    # Try to clear existing relationships
    echo "Clearing role memberships..."
    zed relationship delete role:owner member \
        --endpoint="$SPICEDB_ENDPOINT" \
        --token="$SPICEDB_TOKEN" \
        --insecure 2>/dev/null || echo "No owner memberships to clear"
    
    zed relationship delete role:member member \
        --endpoint="$SPICEDB_ENDPOINT" \
        --token="$SPICEDB_TOKEN" \
        --insecure 2>/dev/null || echo "No member memberships to clear"
    
    echo "Clearing global permissions..."
    zed relationship delete global_settings:system \
        --endpoint="$SPICEDB_ENDPOINT" \
        --token="$SPICEDB_TOKEN" \
        --insecure 2>/dev/null || echo "No global permissions to clear"
    
    echo -e "${GREEN}✅ SpiceDB relationships cleared${NC}"
else
    echo -e "\n${YELLOW}⏭️  Step 2: Skipped (zed CLI not available)${NC}"
fi

# Step 3: Redeploy schema
echo -e "\n${YELLOW}📋 Step 3: Redeploying SpiceDB schema...${NC}"

if [ -f "deploy-permissions-schema.sh" ]; then
    chmod +x deploy-permissions-schema.sh
    if ./deploy-permissions-schema.sh; then
        echo -e "${GREEN}✅ Schema redeployed successfully${NC}"
    else
        echo -e "${RED}❌ Failed to redeploy schema${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ deploy-permissions-schema.sh not found${NC}"
    exit 1
fi

# Step 4: Start server and wait for it to be ready
echo -e "\n${YELLOW}🚀 Step 4: Starting Ki server...${NC}"

# Start server in background
cargo run &
SERVER_PID=$!

echo "Server PID: $SERVER_PID"
echo "Waiting for server to start..."

# Wait for server to be ready (max 30 seconds)
for i in {1..30}; do
    if curl -s "$SERVER_URL/health" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Server is ready${NC}"
        break
    fi
    echo "Waiting... ($i/30)"
    sleep 1
done

# Check if server is actually ready
if ! curl -s "$SERVER_URL/health" >/dev/null 2>&1; then
    echo -e "${RED}❌ Server failed to start within 30 seconds${NC}"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Step 5: Trigger manual sync
echo -e "\n${YELLOW}🔄 Step 5: Triggering manual synchronization...${NC}"

SYNC_RESPONSE=$(curl -s -X POST "$SERVER_URL/sync/trigger" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$SYNC_RESPONSE" | tail -n1)
SYNC_DATA=$(echo "$SYNC_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Manual sync triggered successfully${NC}"
    echo "$SYNC_DATA" | jq '.' 2>/dev/null || echo "$SYNC_DATA"
else
    echo -e "${RED}❌ Failed to trigger sync (HTTP $HTTP_CODE)${NC}"
    echo "$SYNC_DATA"
fi

# Wait a bit for sync to complete
echo "Waiting for sync to complete..."
sleep 3

# Step 6: Run comprehensive tests
echo -e "\n${YELLOW}🧪 Step 6: Running comprehensive tests...${NC}"

if [ -f "test-role-assignment.sh" ]; then
    chmod +x test-role-assignment.sh
    ./test-role-assignment.sh
else
    echo -e "${RED}❌ test-role-assignment.sh not found${NC}"
fi

# Step 7: Test with sample user creation
echo -e "\n${YELLOW}👤 Step 7: Testing user creation and role assignment...${NC}"

# Create a test user (simulating Firebase authentication)
TEST_USER_1='{
    "user_id": "test-firebase-user-1",
    "email": "<EMAIL>",
    "display_name": "Test Owner",
    "photo_url": null
}'

echo "Creating first user (should get Owner role)..."
USER1_RESPONSE=$(curl -s -X POST "$SERVER_URL/users" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "$TEST_USER_1" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$USER1_RESPONSE" | tail -n1)
USER1_DATA=$(echo "$USER1_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "201" ]; then
    echo -e "${GREEN}✅ First user created successfully${NC}"
    echo "$USER1_DATA" | jq '.' 2>/dev/null || echo "$USER1_DATA"
else
    echo -e "${RED}❌ Failed to create first user (HTTP $HTTP_CODE)${NC}"
    echo "$USER1_DATA"
fi

# Wait for role assignment
sleep 2

# Create second user
TEST_USER_2='{
    "user_id": "test-firebase-user-2", 
    "email": "<EMAIL>",
    "display_name": "Test Member",
    "photo_url": null
}'

echo -e "\nCreating second user (should get Member role)..."
USER2_RESPONSE=$(curl -s -X POST "$SERVER_URL/users" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "$TEST_USER_2" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$USER2_RESPONSE" | tail -n1)
USER2_DATA=$(echo "$USER2_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "201" ]; then
    echo -e "${GREEN}✅ Second user created successfully${NC}"
    echo "$USER2_DATA" | jq '.' 2>/dev/null || echo "$USER2_DATA"
else
    echo -e "${RED}❌ Failed to create second user (HTTP $HTTP_CODE)${NC}"
    echo "$USER2_DATA"
fi

# Wait for role assignment
sleep 2

# Step 8: Verify role assignments
echo -e "\n${YELLOW}🔍 Step 8: Verifying role assignments...${NC}"

echo "Checking Owner role members..."
OWNER_CHECK=$(curl -s -X GET "$SERVER_URL/roles/owner" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$OWNER_CHECK" | tail -n1)
OWNER_DATA=$(echo "$OWNER_CHECK" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Owner role details retrieved${NC}"
    MEMBER_COUNT=$(echo "$OWNER_DATA" | jq '.members | length' 2>/dev/null || echo "0")
    echo "Owner role has $MEMBER_COUNT members"
    echo "$OWNER_DATA" | jq '.members' 2>/dev/null || echo "Members: $OWNER_DATA"
else
    echo -e "${RED}❌ Failed to get Owner role details${NC}"
fi

echo -e "\nChecking Member role members..."
MEMBER_CHECK=$(curl -s -X GET "$SERVER_URL/roles/member" \
    -H "Accept: application/json" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$MEMBER_CHECK" | tail -n1)
MEMBER_DATA=$(echo "$MEMBER_CHECK" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Member role details retrieved${NC}"
    MEMBER_COUNT=$(echo "$MEMBER_DATA" | jq '.members | length' 2>/dev/null || echo "0")
    echo "Member role has $MEMBER_COUNT members"
    echo "$MEMBER_DATA" | jq '.members' 2>/dev/null || echo "Members: $MEMBER_DATA"
else
    echo -e "${RED}❌ Failed to get Member role details${NC}"
fi

# Step 9: Test permission checking
echo -e "\n${YELLOW}🔐 Step 9: Testing permission checks...${NC}"

PERMISSION_CHECK='{
    "checks": [
        {
            "resource_type": "global_settings",
            "resource_id": "system",
            "permission": "manage_roles",
            "subject_type": "user",
            "subject_id": "test-firebase-user-1"
        }
    ]
}'

PERM_RESPONSE=$(curl -s -X POST "$SERVER_URL/permissions/check" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "$PERMISSION_CHECK" \
    -w "\n%{http_code}" || echo "000")

HTTP_CODE=$(echo "$PERM_RESPONSE" | tail -n1)
PERM_DATA=$(echo "$PERM_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Permission check completed${NC}"
    echo "$PERM_DATA" | jq '.' 2>/dev/null || echo "$PERM_DATA"
else
    echo -e "${RED}❌ Permission check failed${NC}"
    echo "$PERM_DATA"
fi

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    if [ ! -z "$SERVER_PID" ]; then
        echo "Stopping server (PID: $SERVER_PID)..."
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

echo -e "\n${BLUE}📊 Test Summary${NC}"
echo "==============="
echo "✅ Database reset and schema redeployed"
echo "✅ Server started and sync triggered"
echo "✅ Test users created"
echo "✅ Role assignments verified"
echo "✅ Permission checks tested"

echo -e "\n${GREEN}🎉 Reset and testing completed!${NC}"
echo -e "${YELLOW}💡 The server is still running. Press Ctrl+C to stop it.${NC}"

# Keep server running
wait $SERVER_PID
