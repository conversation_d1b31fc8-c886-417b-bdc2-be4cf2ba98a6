# SpiceDB-SQLite Synchronization Guide

## Overview

This guide explains how to synchronize your SQLite database with SpiceDB permissions to ensure the UI reflects the new comprehensive CRUD permissions system.

## Problem Statement

The UI was showing inconsistent permissions because:
1. **Frontend** calls SpiceDB via `/permissions/check` API ✅
2. **Backend handlers** weren't using SpiceDB permissions ❌
3. **No synchronization** between SQLite user/role data and SpiceDB relationships ❌
4. **Routes** weren't protected with permission middleware ❌

## Solution: Comprehensive Synchronization

### 1. Automatic Sync on Server Startup

The server now automatically synchronizes data when it starts:

```rust
// In main.rs - runs on server startup
sync_service::initialize_sync(db.clone(), spicedb_service.clone()).await
```

**What it does:**
- ✅ Syncs all users from SQLite to SpiceDB
- ✅ Grants self-view permissions to users
- ✅ Sets creator relationships for agents
- ✅ Establishes default admin permissions
- ✅ Registers all entities in SpiceDB

### 2. Manual Sync via API

Three new API endpoints for manual control:

#### Get Sync Status
```bash
GET /sync/status
```
Returns current synchronization statistics and SpiceDB health.

#### Trigger Manual Sync
```bash
POST /sync/trigger
```
Manually triggers a full synchronization between SQLite and SpiceDB.

#### Check SpiceDB Health
```bash
GET /sync/health
```
Verifies SpiceDB connectivity and responsiveness.

### 3. Frontend Integration

#### Admin Panel Component
A new `SyncStatusPanel.vue` component provides:
- Real-time sync status display
- Manual sync trigger button
- SpiceDB health monitoring
- Entity count statistics

#### Usage in Admin Panel
```vue
<template>
  <div class="admin-panel">
    <SyncStatusPanel />
    <!-- Other admin components -->
  </div>
</template>
```

## Step-by-Step Setup

### 1. Deploy Updated Schema
```bash
cd ki
./deploy-permissions-schema.sh
```

### 2. Restart Ki Server
The server will automatically sync on startup:
```bash
# Stop current server
# Start server - sync happens automatically
cargo run
```

### 3. Verify Synchronization
Check the logs for sync messages:
```
INFO ki::services::sync_service: Starting full synchronization between SQLite and SpiceDB
INFO ki::services::sync_service: Synced user: <EMAIL> (uuid)
INFO ki::services::sync_service: Granted admin permissions to user: <EMAIL>
INFO ki::services::sync_service: Full synchronization completed successfully
```

### 4. Test in Frontend
1. Open the admin panel
2. Check the "Database Synchronization" section
3. Verify SpiceDB shows as "Healthy"
4. Confirm entity counts match your database

### 5. Manual Sync (if needed)
If you need to re-sync after making changes:
```bash
curl -X POST http://localhost:3000/sync/trigger \
  -H "Content-Type: application/json" \
  -b "ki_session=your-session-cookie"
```

## What Gets Synchronized

### Users
- **SQLite**: User records with Firebase IDs
- **SpiceDB**: User entities with self-view permissions
- **Result**: Users can view their own profiles

### Agents
- **SQLite**: Agent records with creator references
- **SpiceDB**: Creator relationships
- **Result**: Creators can manage their agents

### Roles
- **SQLite**: Role metadata (name, description, color)
- **SpiceDB**: Role hierarchy and membership
- **Result**: Role-based permissions work correctly

### Default Permissions
- **First User**: Gets global admin permissions
- **System**: Default permission structure established
- **Result**: Admin can manage all entities

## Troubleshooting

### SpiceDB Not Healthy
1. Check SpiceDB is running: `docker ps`
2. Verify connection settings in environment variables
3. Check server logs for SpiceDB connection errors

### Sync Fails
1. Check database connectivity
2. Verify SpiceDB schema is deployed
3. Look for permission errors in logs

### UI Still Shows Wrong Permissions
1. Clear browser cache and cookies
2. Trigger manual sync via API
3. Check browser console for API errors
4. Verify session authentication is working

### No Admin Permissions
If no user has admin permissions:
```bash
# Grant admin to specific user via SpiceDB CLI
zed relationship create global_settings:system admin_user user:firebase-user-id \
  --endpoint="localhost:50051" \
  --token="dev-token" \
  --insecure
```

## Monitoring

### Health Checks
- **SpiceDB**: `/sync/health` endpoint
- **Sync Status**: `/sync/status` endpoint
- **Logs**: Server logs show sync operations

### Metrics
- User count in both systems
- Agent count and creator relationships
- Role count and hierarchy
- Last sync timestamp

## Best Practices

### 1. Regular Monitoring
- Check sync status in admin panel
- Monitor SpiceDB health
- Review server logs for errors

### 2. Backup Before Sync
- Backup SQLite database
- Export SpiceDB relationships if needed

### 3. Test Permissions
- Verify CRUD operations work correctly
- Test with different user roles
- Check task permissions are enforced

### 4. Gradual Rollout
- Test with admin users first
- Gradually enable for all users
- Monitor for permission issues

## Next Steps

1. **Apply Permission Middleware**: Update routes to use permission checking
2. **Update All Handlers**: Replace handlers with permission-aware versions
3. **Test Thoroughly**: Verify all CRUD operations respect permissions
4. **Monitor Performance**: Check SpiceDB response times
5. **Scale as Needed**: Optimize batch operations for large datasets

## API Examples

### Check User Permissions
```javascript
// Frontend code
const hasPermission = await permissionsStore.checkSinglePermission(
    'task',
    'task-123',
    'edit',
    'user',
    currentUserId
);
```

### Bulk Permission Check
```javascript
const checks = [
    { resource_type: 'task', resource_id: 'task-1', permission: 'view', subject_type: 'user', subject_id: userId },
    { resource_type: 'user', resource_id: 'user-2', permission: 'edit', subject_type: 'user', subject_id: userId }
];
const results = await checkPermissions(server, { checks });
```

This synchronization system ensures that your UI permissions are always consistent with the backend SpiceDB authorization system, providing a seamless and secure user experience.
