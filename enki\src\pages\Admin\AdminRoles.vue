<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete, User } from '@element-plus/icons-vue';
import { useRolesStore } from '@/stores/rolesStore';
import { usePermissions } from '@/composables/usePermissions';
import type { Optional, Role } from '@/types';
import RoleHierarchyTree from '@/components/Admin/RoleHierarchyTree.vue';
import RoleDialog from '@/components/Admin/RoleDialog.vue';
import RoleMembersDialog from '@/components/Admin/RoleMembersDialog.vue';
import RolePermissionsDialog from '@/components/Admin/RolePermissionsDialog.vue';

const rolesStore = useRolesStore();
const { canManageRoles, canManageRolesCached } = usePermissions();

// State
const loading = computed(() => rolesStore.loading);
const error = computed(() => rolesStore.error);
const roles = computed(() => rolesStore.roles);
const roleTree = computed(() => rolesStore.roleTree);

// Dialog states
const showMembersDialog = ref(false);
const editingRole = ref<Optional<Role, 'id' | 'created_at' | 'updated_at'> | undefined>();
const selectedRoleForMembers = ref<Role | null>(null);
const showPermissionsDialog = ref(false);
const selectedRoleForPermissions = ref<Role | null>(null);

// Table view state
const viewMode = ref<'table' | 'tree'>('table');

// Check permissions on mount
onMounted(async () => {
    const hasPermission = await canManageRoles();
    if (!hasPermission) {
        ElMessage.error('You do not have permission to manage roles');
        return;
    }
    
    // Load data
    await Promise.all([
        rolesStore.loadRoles(),
        rolesStore.loadRoleTree()
    ]);
});

// Role management actions
const createRole = () => {
    editingRole.value = {
        name: '',
        description: '',
        color: '#6b7280'
    };
};

const editRole = (role: Role) => {
    editingRole.value = role;
};

const deleteRole = async (role: Role) => {
    try {
        await ElMessageBox.confirm(
            `Are you sure you want to delete the role "${role.name}"? This action cannot be undone.`,
            'Confirm Delete',
            {
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }
        );
        
        const success = await rolesStore.deleteRole(role.id);
        if (success) {
            ElMessage.success('Role deleted successfully');
            // Reload data
            await Promise.all([
                rolesStore.loadRoles(),
                rolesStore.loadRoleTree()
            ]);
        }
    } catch (error) {
        // User cancelled or error occurred
        if (error !== 'cancel') {
            console.error('Failed to delete role:', error);
        }
    }
};

const manageMembers = (role: Role) => {
    selectedRoleForMembers.value = role;
    showMembersDialog.value = true;
};

const managePermissions = async (role: Role) => {
    selectedRoleForPermissions.value = role;
    showPermissionsDialog.value = true;
    await rolesStore.loadRolePermissions(role.id);
};

// Dialog handlers
const handleRoleDialogClose = async (saved: Role | null) => {
    editingRole.value = undefined;
    
    if (saved) {
        // Reload data
        await Promise.all([
            rolesStore.loadRoles(),
            rolesStore.loadRoleTree()
        ]);
    }
};

const handleMembersDialogClose = () => {
    showMembersDialog.value = false;
    selectedRoleForMembers.value = null;
};

const handlePermissionsDialogClose = () => {
    showPermissionsDialog.value = false;
    selectedRoleForPermissions.value = null;
};

// Utility functions
const getRoleColor = (color?: string) => {
    return color || '#6b7280';
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
};
</script>

<template>
    <div class="admin-roles">
        <!-- Header -->
        <div class="page-header">
            <div class="header-content">
                <h1>Role Management</h1>
                <p class="header-description">
                    Manage roles and permissions for your organization
                </p>
            </div>
            <div class="header-actions">
                <el-button-group>
                    <el-button 
                        :type="viewMode === 'table' ? 'primary' : 'default'"
                        @click="viewMode = 'table'"
                    >
                        Table View
                    </el-button>
                    <el-button 
                        :type="viewMode === 'tree' ? 'primary' : 'default'"
                        @click="viewMode = 'tree'"
                    >
                        Hierarchy View
                    </el-button>
                </el-button-group>
                <el-button 
                    type="primary" 
                    :icon="Plus" 
                    @click="createRole"
                    :disabled="!canManageRolesCached"
                >
                    Create Role
                </el-button>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-container">
            <el-alert :title="error" type="error" :closable="false" show-icon />
        </div>

        <!-- Content -->
        <div v-else class="content">
            <!-- Table View -->
            <el-card v-if="viewMode === 'table'" class="roles-table-card">
                <template #header>
                    <div class="card-header">
                        <span>Roles ({{ roles.length }})</span>
                    </div>
                </template>
                
                <el-table :data="roles" style="width: 100%">
                    <el-table-column label="Role" min-width="200">
                        <template #default="{ row }">
                            <div class="role-info">
                                <div 
                                    class="role-color-indicator" 
                                    :style="{ backgroundColor: getRoleColor(row.color) }"
                                ></div>
                                <div>
                                    <div class="role-name">{{ row.name }}</div>
                                    <div class="role-description" v-if="row.description">
                                        {{ row.description }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="Created" width="120">
                        <template #default="{ row }">
                            {{ formatDate(row.created_at) }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="Actions" width="200" align="right">
                        <template #default="{ row }">
                            <el-button-group>
                                <el-button
                                    size="small"
                                    :icon="User"
                                    @click="manageMembers(row)"
                                    title="Manage Members"
                                >
                                    Members
                                </el-button>
                                <el-button
                                    size="small"
                                    :icon="Edit"
                                    @click="managePermissions(row)"
                                    :disabled="!canManageRolesCached"
                                    title="Permissions"
                                >
                                    Perms
                                </el-button>
                                <el-button
                                    size="small"
                                    :icon="Edit"
                                    @click="editRole(row)"
                                    :disabled="!canManageRolesCached"
                                    title="Edit Role"
                                />
                                <el-button 
                                    size="small" 
                                    :icon="Delete" 
                                    type="danger" 
                                    @click="deleteRole(row)"
                                    :disabled="!canManageRolesCached"
                                    title="Delete Role"
                                />
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- Tree View -->
            <el-card v-else class="roles-tree-card">
                <template #header>
                    <div class="card-header">
                        <span>Role Hierarchy</span>
                    </div>
                </template>
                
                <RoleHierarchyTree 
                    :tree="roleTree"
                    :can-manage="canManageRolesCached"
                    @edit-role="editRole"
                    @delete-role="deleteRole"
                    @manage-members="manageMembers"
                />
            </el-card>
        </div>

        <!-- Dialogs -->
        <RoleDialog
            :role="editingRole"
            @save="handleRoleDialogClose"
        />
        
        <RoleMembersDialog
            v-model="showMembersDialog"
            :role="selectedRoleForMembers"
            @close="handleMembersDialogClose"
        />
        <RolePermissionsDialog
            v-model="showPermissionsDialog"
            :role="selectedRoleForPermissions"
            @close="handlePermissionsDialogClose"
        />
    </div>
</template>

<style scoped>
.admin-roles {
    padding: 24px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
}

.header-content h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
}

.header-description {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.loading-container,
.error-container {
    margin: 24px 0;
}

.content {
    margin-top: 24px;
}

.card-header {
    font-weight: 600;
    color: var(--el-text-color-primary);
}

.role-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.role-color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.role-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
}

.role-description {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 2px;
}

.roles-table-card,
.roles-tree-card {
    box-shadow: var(--el-box-shadow-light);
}
</style>
