# Role-Based Permissions System Guide

## Overview

This guide explains the comprehensive role-based permissions system with automatic role assignment and atomic CRUD permission management.

## System Architecture

### 1. **Default Roles**

#### Owner Role
- **Purpose**: Full system ownership with all administrative privileges
- **Assignment**: Automatically assigned to the first user who registers
- **Permissions**: 
  - All global settings management (users, agents, tasks, roles)
  - Full CRUD on all entities
  - System administration capabilities

#### Member Role
- **Purpose**: Standard member access with basic permissions
- **Assignment**: Automatically assigned to all new users after the first
- **Permissions**:
  - Create and manage own tasks
  - View own profile
  - Basic system interaction

### 2. **Automatic Role Assignment**

#### First User (Owner)
```rust
// In UserService::ensure_user_exists()
if is_first_user {
    // Assigns Owner role via SyncService
    sync_service.assign_owner_role(&user.user_id).await?;
}
```

#### Subsequent Users (Members)
```rust
// In UserService::ensure_user_exists() and SyncService::sync_user()
sync_service.assign_member_role(&user.user_id).await?;
```

### 3. **Atomic Permission Management UI**

#### AtomicRolePermissionsDialog Features
- **Resource Types**: Task, User, Agent, Role, Global Settings
- **CRUD Operations**: Create, Read (View), Update (Edit), Delete
- **Global vs Specific**: Grant permissions globally or for specific resources
- **Atomic Transactions**: All changes applied together or not at all
- **Pending Changes**: Preview changes before applying

## Usage Guide

### 1. **Setting Up the System**

#### Deploy Schema
```bash
cd ki
./deploy-permissions-schema.sh
```

#### Start Server
```bash
cargo run
```
The server will automatically:
- Create Owner and Member roles in SQLite
- Set up SpiceDB permissions for roles
- Assign Owner role to first user
- Sync all existing data

### 2. **Managing Role Permissions**

#### Access Admin Panel
1. Navigate to `/admin/roles`
2. Click "CRUD" button next to any role
3. Use the Atomic Role Permissions Dialog

#### Grant Global Permissions
1. Select resource type (e.g., "Task")
2. Check "Global Permission" 
3. Click "Add to Pending"
4. Click "Apply Changes"

**Example**: Grant global task management to a role
- Resource Type: Task
- Global Permission: ✓ (automatically sets to "manage_tasks")
- Resource ID: system (automatic)

#### Grant Specific Resource Permissions
1. Select resource type (e.g., "Task")
2. Enter specific resource ID (e.g., "task-123")
3. Select permission level (e.g., "edit")
4. Click "Add to Pending"
5. Click "Apply Changes"

### 3. **Permission Levels**

#### Task Permissions
- **create**: Can create new tasks
- **view**: Can view task details
- **edit**: Can modify task properties
- **delete**: Can remove tasks
- **admin**: Full task administration
- **owner**: Task ownership (highest level)

#### User Permissions
- **create**: Can create new users
- **view**: Can view user profiles
- **edit**: Can modify user information
- **delete**: Can remove users
- **admin**: Full user administration

#### Global Permissions
- **manage_users**: Global user management
- **manage_agents**: Global agent management
- **manage_tasks**: Global task management
- **manage_roles**: Global role management

### 4. **Frontend Integration**

#### Check Permissions in Components
```vue
<script setup>
import { usePermissions } from '@/composables/usePermissions';

const { hasPermission } = usePermissions();

// Check specific permission
const canEditTask = await hasPermission('task', 'task-123', 'edit');

// Check global permission
const canManageUsers = await hasPermission('global_settings', 'system', 'manage_users');
</script>
```

#### Permission-Based Rendering
```vue
<template>
  <el-button 
    v-permission="{ resource: 'task', resourceId: task.id, permission: 'edit' }"
    @click="editTask"
  >
    Edit Task
  </el-button>
</template>
```

### 5. **API Integration**

#### Check Permissions via API
```javascript
import { checkPermissions } from '@/services/kiApi';

const checks = [
  {
    resource_type: 'task',
    resource_id: 'task-123',
    permission: 'edit',
    subject_type: 'user',
    subject_id: currentUserId
  }
];

const results = await checkPermissions(server, { checks });
```

#### Bulk Permission Operations
```javascript
import { bulkRelationshipOperations } from '@/services/kiApi';

const operations = [
  {
    operation: 'create',
    relationships: [{
      resource_type: 'role',
      resource_id: 'manager',
      relation: 'member',
      subject_type: 'user',
      subject_id: userId
    }]
  }
];

await bulkRelationshipOperations(server, operations);
```

## Advanced Features

### 1. **Role Hierarchy**
- Roles can have parent-child relationships
- Child roles inherit permissions from parents
- Managed through the role hierarchy tree view

### 2. **Batch Operations**
- Multiple permission changes in single transaction
- Optimized SpiceDB batch checking
- Atomic success/failure for consistency

### 3. **Permission Inheritance**
- Role members inherit all role permissions
- Hierarchical roles provide transitive permissions
- Efficient permission resolution

### 4. **Audit Trail**
- All permission changes logged
- SpiceDB provides built-in audit capabilities
- Traceable permission modifications

## Troubleshooting

### 1. **No Owner Permissions**
If the first user doesn't have owner permissions:
```bash
# Manually assign owner role via SpiceDB CLI
zed relationship create role:owner member user:firebase-user-id \
  --endpoint="localhost:50051" \
  --token="dev-token" \
  --insecure
```

### 2. **Permission Denied Errors**
1. Check user has appropriate role membership
2. Verify role has required permissions
3. Check SpiceDB connectivity
4. Review server logs for permission check failures

### 3. **Sync Issues**
1. Check `/sync/status` endpoint
2. Trigger manual sync via `/sync/trigger`
3. Verify SpiceDB health via `/sync/health`
4. Check server logs for sync errors

### 4. **UI Not Reflecting Permissions**
1. Clear browser cache and cookies
2. Check browser console for API errors
3. Verify session authentication
4. Refresh permission cache in frontend

## Best Practices

### 1. **Role Design**
- Keep roles focused and specific
- Use hierarchy for permission inheritance
- Avoid overly complex permission structures

### 2. **Permission Granularity**
- Use global permissions for system-wide access
- Use specific permissions for fine-grained control
- Balance security with usability

### 3. **Testing**
- Test permission changes in development first
- Verify all CRUD operations work correctly
- Test with different user roles

### 4. **Monitoring**
- Monitor permission check performance
- Watch for permission denied patterns
- Track role membership changes

This system provides a robust, scalable foundation for comprehensive role-based access control with automatic role assignment and user-friendly permission management.
